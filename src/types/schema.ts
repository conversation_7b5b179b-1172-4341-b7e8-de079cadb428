export interface FincloudLibrarySchema {
  version: string;
  lastUpdated: Date;
  libraries: {
    ui: UILibrarySchema;
    utils: UtilsLibrarySchema;
  };
}

export interface UILibrarySchema {
  components: ComponentRegistry;
  modules: ModuleRegistry;
  assets: AssetRegistry;
  styles: StyleRegistry;
}

export interface UtilsLibrarySchema {
  functions: FunctionRegistry;
  services: ServiceRegistry;
  pipes: PipeRegistry;
  decorators: DecoratorRegistry;
  operators: OperatorRegistry;
}

export interface ComponentRegistry {
  [componentName: string]: {
    metadata: ComponentMetadata;
    api: ComponentAPI;
    relationships: ComponentRelationships;
    examples: CodeExample[];
    tests: TestMetadata[];
  };
}

export interface ComponentMetadata {
  name: string;
  selector: string;
  modulePath: string;
  exportPath: string;
  filePath: string;
  dependencies: string[];
  documentation: string;
  storybookUrl?: string;
  category: string;
  tags: string[];
  deprecated?: boolean;
  since?: string;
}

export interface ComponentAPI {
  selector: string;
  inputs: APIProperty[];
  outputs: APIProperty[];
  methods: APIMethod[];
  contentProjection: ContentSlot[];
  hostBindings: HostBinding[];
}

export interface APIProperty {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  documentation: string;
  examples: string[];
  deprecated?: boolean;
}

export interface APIMethod {
  name: string;
  signature: string;
  parameters: MethodParameter[];
  returnType: string;
  documentation: string;
  isPublic: boolean;
}

export interface MethodParameter {
  name: string;
  type: string;
  optional: boolean;
  documentation?: string;
}

export interface ContentSlot {
  name: string;
  selector?: string;
  documentation: string;
  required: boolean;
}

export interface HostBinding {
  property: string;
  expression: string;
  documentation?: string;
}

export interface ComponentRelationships {
  dependencies: string[];
  dependents: string[];
  relatedComponents: string[];
  alternatives: string[];
  commonlyUsedWith: string[];
}

export interface CodeExample {
  title: string;
  description: string;
  category:
    | "basic"
    | "advanced"
    | "form-integration"
    | "styling"
    | "accessibility";
  code: {
    typescript?: string;
    template?: string;
    styles?: string;
  };
  dependencies: string[];
  stackblitzUrl?: string;
}

export interface TestMetadata {
  filePath: string;
  testCases: string[];
  coverage?: number;
}

export interface ModuleRegistry {
  [moduleName: string]: {
    path: string;
    exports: string[];
    imports: string[];
    providers: string[];
    declarations: string[];
  };
}

export interface AssetRegistry {
  [assetName: string]: {
    path: string;
    type: "svg" | "png" | "jpg" | "font" | "other";
    usage: string[];
  };
}

export interface StyleRegistry {
  [styleName: string]: {
    path: string;
    variables: CSSVariable[];
    mixins: CSSMixin[];
    classes: CSSClass[];
  };
}

export interface CSSVariable {
  name: string;
  value: string;
  description?: string;
}

export interface CSSMixin {
  name: string;
  parameters: string[];
  description: string;
}

export interface CSSClass {
  name: string;
  properties: string[];
  description?: string;
}

export interface FunctionRegistry {
  [functionName: string]: {
    signature: FunctionSignature;
    usage: UsageExample[];
    relatedFunctions: string[];
    category: string;
    filePath: string;
    exportPath: string;
  };
}

export interface FunctionSignature {
  name: string;
  parameters: MethodParameter[];
  returnType: string;
  typeParameters?: string[];
  documentation: string;
}

export interface UsageExample {
  code: string;
  description: string;
  context: string;
}

export interface ServiceRegistry {
  [serviceName: string]: {
    className: string;
    filePath: string;
    exportPath: string;
    methods: APIMethod[];
    properties: APIProperty[];
    injectionToken?: string;
    documentation: string;
  };
}

export interface PipeRegistry {
  [pipeName: string]: {
    name: string;
    filePath: string;
    exportPath: string;
    signature: FunctionSignature;
    examples: UsageExample[];
    pure: boolean;
  };
}

export interface DecoratorRegistry {
  [decoratorName: string]: {
    name: string;
    filePath: string;
    exportPath: string;
    usage: string;
    parameters: MethodParameter[];
    documentation: string;
  };
}

export interface OperatorRegistry {
  [operatorName: string]: {
    name: string;
    filePath: string;
    exportPath: string;
    signature: FunctionSignature;
    usage: UsageExample[];
    category:
      | "creation"
      | "transformation"
      | "filtering"
      | "combination"
      | "utility";
  };
}

export interface CompletionItem {
  label: string;
  kind: CompletionItemKind;
  detail?: string;
  documentation?: string;
  insertText?: string;
  sortText?: string;
  filterText?: string;
  additionalTextEdits?: TextEdit[];
}

export enum CompletionItemKind {
  Text = 1,
  Method = 2,
  Function = 3,
  Constructor = 4,
  Field = 5,
  Variable = 6,
  Class = 7,
  Interface = 8,
  Module = 9,
  Property = 10,
  Unit = 11,
  Value = 12,
  Enum = 13,
  Keyword = 14,
  Snippet = 15,
  Color = 16,
  File = 17,
  Reference = 18,
  Folder = 19,
  EnumMember = 20,
  Constant = 21,
  Struct = 22,
  Event = 23,
  Operator = 24,
  TypeParameter = 25,
}

export interface TextEdit {
  range: Range;
  newText: string;
}

export interface Range {
  start: Position;
  end: Position;
}

export interface Position {
  line: number;
  character: number;
}

export interface ImportSuggestion {
  importPath: string;
  namedImports: string[];
  isDefault: boolean;
  confidence: number;
}

export interface ComponentSuggestion {
  name: string;
  confidence: number;
  reason: string;
  usage: string;
}

export interface AnalysisResult {
  type: "error" | "warning" | "info";
  message: string;
  suggestion?: string;
  fix?: CodeFix;
  range?: Range;
}

export interface CodeFix {
  title: string;
  edits: TextEdit[];
}

export interface ComponentDocumentation {
  overview: string;
  api: APIReference;
  examples: CodeExample[];
  relatedComponents: string[];
}

export interface APIReference {
  selector: string;
  inputs: APIProperty[];
  outputs: APIProperty[];
  methods: APIMethod[];
  contentProjection: ContentSlot[];
}
